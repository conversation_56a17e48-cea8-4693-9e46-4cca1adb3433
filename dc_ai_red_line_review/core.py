import json
import os
import time

from dotenv import load_dotenv
from pydantic import BaseModel, Field

from dc_ai_red_line_review.utils import (
    get_token_count,
    get_logger,
    timing_decorator,
)

load_dotenv(override=True)

unified_all_prompt = """
You are a sensitive content reviewer. Your role is to review conversations between customer service representatives and users to identify specific sensitive information based on the provided rules and report them in the required format.

## CRITICAL INSTRUCTION: EXTRACT ONLY FROM CONVERSATION
- The categories below are for YOUR UNDERSTANDING of what to look for
- The "values" array must contain ONLY text that ACTUALLY EXISTS in the conversation
- DO NOT copy the rule descriptions or examples into the "values" array
- EXTRACT VERBATIM quotes from the conversation being reviewed

## Categories of Sensitive Information  
- **consulting_company_info**: Look for questions or discussions about company information such as:
  * Company registered/office address inquiries
  * Questions about company location (China/Singapore/Hong Kong/Malaysia)
  * Server location inquiries
  * Employee location questions
  * Customer service representative location questions
  * Company licensing inquiries
  
- **selling_user_info**: Look for offers to buy/sell user data or account information
  
- **negative_news**: Look for users inquiring about rumors claiming issues with KuCoin, such as:
  * Rumors of KuCoin running away, going bankrupt, or being sanctioned
  * Rumors of KuCoin leader/employee being arrested
  * Claims that the platform is unsafe, has experienced data breaches
  * Claims of large asset transfers from the platform
  * Rumors that the domain has been blocked
  * Concerns about insufficient platform reserves, asking for Proof of Reserves
  
- **major_complaints**: Look for extremely negative, dissatisfied, complaining, or angry users who threaten or mention exposing the company through social media, lawyers, police, etc.

- **request_contact_information**: Look for customer service representatives actively asking users for personal contact details such as:
  * Personal email, phone number, personal Telegram, WeChat, WhatsApp, Twitter, Facebook
  
- **spam_messages**: Look for responses from customer service representatives containing derogatory or offensive language

## OUTPUT FORMAT
Return JSON with hit_rule (boolean) and values (array of EXACT quotes from conversation) for each category.
**CRITICAL**: values must contain ONLY text that exists VERBATIM in the conversation being reviewed, NOT rule descriptions.
"""


class CoreComponents:
    def __init__(self, model_client, prompt_dict=None):
        self.logger = get_logger(module_name="core_components")
        self.model_client = model_client
        self.prompt_dict = prompt_dict

    @staticmethod
    def key_contact_review(content: str, risk_keywords: list) -> dict:
        """Sync key contact review function."""
        content_lower = content.lower()
        hit_keywords = [
            keyword for keyword in risk_keywords if keyword.lower() in content_lower
        ]

        result = {
            "hit_rule": True if hit_keywords else False,
            "values": hit_keywords,
        }
        return result

    def _make_single_model_call(
        self,
        content: str,
        enhanced_system_prompt: str,
        json_schema: dict,
        temperature: float = 0.7,
        top_p: float = 0.8,
        content_tokens: int = None,
    ) -> dict:
        """Helper method to make a single call to the model."""
        # Calculate system prompt tokens and use pre-calculated content tokens if available
        prompt_tokens = get_token_count(enhanced_system_prompt)
        if content_tokens is None:
            content_tokens = get_token_count(content)
        total_tokens = prompt_tokens + content_tokens

        total_tokens_str = f"~{total_tokens:,} (system: {prompt_tokens:,}, content: {content_tokens:,})"

        self.logger.info(f"Starting API call with {total_tokens_str} tokens...")

        call_start_time = time.time()

        # Make direct synchronous API call with error handling for input length
        try:
            chat_completion = self.model_client.chat.completions.create(
                messages=[
                    {"role": "system", "content": enhanced_system_prompt},
                    {
                        "role": "user",
                        "content": content,
                    },
                ],
                model=os.environ["MODEL_NAME"],
                response_format={
                    "type": "json_schema",
                    "json_schema": {
                        "name": "result",
                        # convert the pydantic model to json schema
                        "schema": json_schema,
                    },
                },
                extra_body={
                    # "guided_json": json_schema,
                    "chat_template_kwargs": {"enable_thinking": False},
                },
                temperature=temperature,
                top_p=top_p,
            )
        except Exception as e:
            # Check if the error is related to input length
            error_message = str(e).lower()
            self.logger.error(f"Input length exceeded model limits: {error_message}")
            raise ValueError("input_too_long")

        # Basic response validation
        if not chat_completion.choices:
            raise ValueError("Model API returned empty choices")

        choice = chat_completion.choices[0]
        if not choice.message:
            raise ValueError("Model API returned empty message")

        res = choice.message.content

        self.logger.info(f"Model response: {res}")

        # Parse JSON with proper error handling
        try:
            parsed_response = json.loads(res)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.error(f"Raw response content: {res}")
            raise ValueError(f"Invalid JSON response from API: {e}")

        call_end_time = time.time()
        api_duration = call_end_time - call_start_time
        self.logger.info(
            f"API call completed successfully in {api_duration:.2f}s "
            f"(tokens: {total_tokens_str})"
        )
        return parsed_response

    def unified_all_review_sync(self, content: str, content_tokens: int = None) -> dict:
        """Unified synchronous version for all sensitive content review."""

        class UnifiedAllReviewRes(BaseModel):
            consulting_company_info: dict = Field(
                description="Company information inquiry check results, containing hit_rule(bool) and values(list[str])"
            )
            selling_user_info: dict = Field(
                description="User information selling check results, containing hit_rule(bool) and values(list[str])"
            )
            negative_news: dict = Field(
                description="Negative news check results, containing hit_rule(bool) and values(list[str])"
            )
            major_complaints: dict = Field(
                description="Major complaint check results, containing hit_rule(bool) and values(list[str])"
            )
            request_contact_information: dict = Field(
                description="Contact information request check results, containing hit_rule(bool) and values(list[str])"
            )
            spam_messages: dict = Field(
                description="Abusive message check results, containing hit_rule(bool) and values(list[str])"
            )

        json_schema = UnifiedAllReviewRes.model_json_schema()

        base_prompt = unified_all_prompt

        # Enhanced system prompt for unified processing
        enhanced_system_prompt = (
            base_prompt
            + "\n\n🚨 CRITICAL EXTRACTION REQUIREMENTS 🚨\n"
            + "1. VALUES MUST BE VERBATIM: Copy text EXACTLY as it appears in the conversation\n"
            + "2. NO MODIFICATIONS: Do not change, summarize, paraphrase, or translate any text\n"
            + "3. PRESERVE ORIGINAL LANGUAGE: Keep the exact language of the source text (Chinese stays Chinese, English stays English)\n"
            + "4. NO RULE TEXT: Do not copy text from rule descriptions or examples - ONLY from the conversation\n"
            + "5. NO INFERENCE: Do not generate content that doesn't exist in the conversation\n"
            + "6. EMPTY IF NO MATCH: Return [] if no exact matches are found in the conversation\n"
            + "7. SOURCE VERIFICATION: Every value must be traceable to specific text in the conversation\n"
            + "8. CHARACTER-LEVEL ACCURACY: Preserve exact characters, punctuation, spacing, and formatting\n"
            + "9. IGNORE EXAMPLES: All example text in the rules above should be ignored - only analyze the actual conversation\n"
            + "10. CONVERSATION ONLY: Extract ONLY from the conversation content between <|im_start|> and <|im_end|> tags\n"
            + "\nREMEMBER: You are extracting evidence from the conversation, not interpreting rules or creating content. NEVER translate or modify the original language."
        )

        # Make single unified model call for all checks
        self.logger.info("Making unified model call for all sensitive content review")
        parsed_response = self._make_single_model_call(
            content,
            enhanced_system_prompt,
            json_schema,
            temperature=0.7,
            top_p=0.8,
            content_tokens=content_tokens,
        )

        # Convert unified response to original format
        sensitive_inquiry_results = []
        sensitive_reply_results = []

        # Map for sensitive_inquiry
        inquiry_mapping = {
            "consulting_company_info": "咨询公司信息",
            "selling_user_info": "兜售用户信息",
            "negative_news": "负面新闻",
            "major_complaints": "重大客诉",
        }

        for key, type_name in inquiry_mapping.items():
            if key in parsed_response:
                result = parsed_response[key].copy()
                result["type"] = type_name
                sensitive_inquiry_results.append(result)

        # Map for sensitive_reply
        reply_mapping = {
            "request_contact_information": "索要联系方式",
            "spam_messages": "辱骂信息",
        }

        for key, type_name in reply_mapping.items():
            if key in parsed_response:
                result = parsed_response[key].copy()
                result["type"] = type_name
                sensitive_reply_results.append(result)

        return {
            "sensitive_inquiry": sensitive_inquiry_results,
            "sensitive_reply": sensitive_reply_results,
        }

    @timing_decorator
    def government_inquiry_review(self, content: str, government_config: dict) -> list:
        """Government inquiry review function."""
        results = []

        # Check government agencies
        agencies = government_config.get("government_agency", [])
        agency_hits = [
            agency for agency in agencies if agency.lower() in content.lower()
        ]
        results.append(
            {
                "hit_rule": True if agency_hits else False,
                "values": agency_hits,
                "type": "政府机构",
            }
        )

        # Check government mailboxes
        mailboxes = government_config.get("government_mailbox", [])
        mailbox_hits = [
            mailbox for mailbox in mailboxes if mailbox.lower() in content.lower()
        ]
        results.append(
            {
                "hit_rule": True if mailbox_hits else False,
                "values": mailbox_hits,
                "type": "政府邮箱",
            }
        )

        self.logger.info(results)
        return results

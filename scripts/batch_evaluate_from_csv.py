#!/usr/bin/env python3
"""
基于CSV结果文件进行完整的Azure OpenAI批量评测
从data/text_format获取对话内容，对已有的审查结果进行LLM评测
"""

import json
import os
import time
import csv
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
from pprint import pprint

from openai import AzureOpenAI

# Import Chonkie for text chunking
try:
    from chonkie import TokenChunker

    CHONKIE_AVAILABLE = True
except ImportError:
    CHONKIE_AVAILABLE = False
    print("⚠️ Chonkie not available, long text handling will be limited")


# Azure OpenAI配置
AZURE_CONFIG = {
    "api_key": "********************************",
    "azure_endpoint": "https://dc-sit-arithmetic-group.openai.azure.com/",
    "api_version": "2024-08-01-preview",
    "model": "gpt-4o-1120",
}


def load_text_format_case(case_id: str, text_format_dir: Path) -> str:
    """从text_format目录加载案例的对话内容"""
    case_file = text_format_dir / f"case_{case_id}.txt"

    if not case_file.exists():
        print(f"⚠️ 案例文件不存在: {case_file}")
        return ""

    try:
        with open(case_file, "r", encoding="utf-8") as f:
            content = f.read().strip()
        return content
    except Exception as e:
        print(f"❌ 读取案例文件失败 {case_file}: {e}")
        return ""


def parse_text_format_content(content: str) -> List[Dict[str, Any]]:
    """解析text_format格式的对话内容为消息列表"""
    messages = []

    # 按<|im_start|>分割内容
    parts = content.split("<|im_start|>")

    for i, part in enumerate(parts):
        if not part.strip():
            continue

        # 查找<|im_end|>
        if "<|im_end|>" in part:
            content_part = part.split("<|im_end|>")[0].strip()
        else:
            content_part = part.strip()

        # 提取消息类型和内容
        lines = content_part.split("\n", 1)
        if len(lines) >= 2:
            msg_type = lines[0].strip()
            msg_content = lines[1].strip()
        elif len(lines) == 1:
            # 如果只有一行，可能是类型和内容在同一行
            if content_part.startswith(("USER", "AGENT")):
                parts_split = content_part.split("\n", 1)
                if len(parts_split) == 2:
                    msg_type = parts_split[0].strip()
                    msg_content = parts_split[1].strip()
                else:
                    msg_type = "UNKNOWN"
                    msg_content = content_part
            else:
                msg_type = "UNKNOWN"
                msg_content = content_part
        else:
            continue

        if msg_content:
            messages.append({"id": i, "type": msg_type, "msg": msg_content})

    return messages


def load_csv_results(csv_file: str) -> List[Dict[str, Any]]:
    """加载CSV结果文件"""
    try:
        results = []
        with open(csv_file, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            for row in reader:
                results.append(dict(row))
        return results
    except Exception as e:
        print(f"❌ 加载CSV文件失败: {e}")
        return []


def parse_csv_row_to_review_result(row: Dict[str, str]) -> Dict[str, Any]:
    """将CSV行数据解析为review_result格式"""
    review_res = {}

    # 解析key_contact
    if "key_contact_hit_rule" in row:
        review_res["key_contact"] = {
            "hit_rule": row.get("key_contact_hit_rule", "False").lower() == "true",
            "values": row.get("key_contact_values", "").split("|")
            if row.get("key_contact_values")
            else [],
            "matched_ids": row.get("key_contact_matched_ids", "[]"),
        }

    # 解析internal_system
    if "internal_system_hit_rule" in row:
        review_res["internal_system"] = {
            "hit_rule": row.get("internal_system_hit_rule", "False").lower() == "true",
            "values": row.get("internal_system_values", "").split("|")
            if row.get("internal_system_values")
            else [],
            "matched_ids": row.get("internal_system_matched_ids", "[]"),
        }

    # 解析列表类型的结果 (sensitive_inquiry, sensitive_reply, government_inquiry)
    for category in ["sensitive_inquiry", "sensitive_reply", "government_inquiry"]:
        category_results = []

        # 查找该类别的所有子类型
        for key in row.keys():
            if key.startswith(f"{category}_") and key.endswith("_hit_rule"):
                # 提取子类型名称
                subtype = key[len(f"{category}_") : -len("_hit_rule")]

                hit_rule = (
                    row.get(f"{category}_{subtype}_hit_rule", "False").lower() == "true"
                )
                values = (
                    row.get(f"{category}_{subtype}_values", "").split("|")
                    if row.get(f"{category}_{subtype}_values")
                    else []
                )
                matched_ids = row.get(f"{category}_{subtype}_matched_ids", "[]")

                category_results.append(
                    {
                        "type": subtype,
                        "hit_rule": hit_rule,
                        "values": values,
                        "matched_ids": matched_ids,
                    }
                )

        if category_results:
            review_res[category] = category_results

    return review_res


class AzureEvaluator:
    """Azure OpenAI评测器"""

    def __init__(self):
        self.client = AzureOpenAI(
            api_key=AZURE_CONFIG["api_key"],
            azure_endpoint=AZURE_CONFIG["azure_endpoint"],
            api_version=AZURE_CONFIG["api_version"],
        )
        self.model = AZURE_CONFIG["model"]

        # Initialize Chonkie chunker if available
        if CHONKIE_AVAILABLE:
            self.chunker = TokenChunker(
                tokenizer="character",
                chunk_size=6000,  # Conservative limit for Azure OpenAI
                chunk_overlap=500,
            )
        else:
            self.chunker = None

    def format_messages_for_evaluation(self, messages: List[Dict[str, Any]]) -> str:
        """将消息格式化为评测用的文本"""
        formatted_parts = []
        for msg in messages:
            msg_type = msg.get("type", "UNKNOWN")
            msg_content = msg.get("msg", "")
            formatted_parts.append(f"[{msg_type}]: {msg_content}")
        return "\n".join(formatted_parts)

    def format_review_result(self, review_res: Dict[str, Any]) -> str:
        """将审查结果格式化为评测用的文本"""
        result_parts = []

        for category, data in review_res.items():
            result_parts.append(f"\n=== {category.upper()} ===")

            if isinstance(data, dict) and "hit_rule" in data:
                # key_contact, internal_system类型
                hit_rule = data.get("hit_rule", False)
                values = data.get("values", [])
                result_parts.append(f"命中规则: {hit_rule}")
                if values:
                    result_parts.append(f"检测到的值: {', '.join(values)}")

            elif isinstance(data, list):
                # sensitive_inquiry, sensitive_reply, government_inquiry类型
                for item in data:
                    if isinstance(item, dict):
                        item_type = item.get("type", "unknown")
                        hit_rule = item.get("hit_rule", False)
                        values = item.get("values", [])
                        result_parts.append(f"  {item_type}: 命中={hit_rule}")
                        if values:
                            result_parts.append(f"    检测值: {', '.join(values)}")

        return "\n".join(result_parts)

    def evaluate_result(
        self, messages: List[Dict[str, Any]], review_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """使用Azure OpenAI评测审查结果的准确性，支持长文本分块处理"""
        try:
            # 格式化输入数据
            conversation_text = self.format_messages_for_evaluation(messages)
            review_text = self.format_review_result(review_result.get("review_res", {}))

            # 检查文本长度，如果太长则进行分块处理
            if self._is_text_too_long(conversation_text):
                print(f"⚠️ 对话文本过长 ({len(conversation_text)} 字符)，使用分块处理")
                return self._evaluate_with_chunking(conversation_text, review_text)
            else:
                return self._evaluate_single_text(conversation_text, review_text)

        except Exception as e:
            print(f"❌ Azure OpenAI评测失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _is_text_too_long(self, text: str, max_chars: int = 20000) -> bool:
        """检查文本是否过长需要分块处理"""
        return len(text) > max_chars

    def _evaluate_single_text(self, conversation_text: str, review_text: str) -> Dict[str, Any]:
        """评测单个文本（不需要分块）"""
        # 构建评测提示词
        evaluation_prompt = f"""
你是一个专业的客服对话审查评测专家。请评估以下红线审查系统的检测结果是否准确。

**对话内容:**
{conversation_text}

**系统检测结果:**
{review_text}

**评测维度说明:**
1. key_contact: 检测联系方式信息（电话、微信、QQ、邮箱等）
2. sensitive_inquiry: 检测用户敏感询问（咨询公司信息、兜售用户信息、负面新闻、重大客诉等）
3. sensitive_reply: 检测客服敏感回复（索要联系方式、辱骂信息等）
4. government_inquiry: 检测政府相关询问（政府机构、政府邮箱等）
5. internal_system: 检测内部系统信息泄露

**评测要求:**
请对每个检测维度的结果进行评估，判断系统的检测是否准确。

**输出格式（严格按照以下JSON格式）:**
{{
  "key_contact": {{"correct": true/false, "reason": "评估理由"}},
  "sensitive_inquiry": {{"correct": true/false, "reason": "评估理由"}},
  "sensitive_reply": {{"correct": true/false, "reason": "评估理由"}},
  "government_inquiry": {{"correct": true/false, "reason": "评估理由"}},
  "internal_system": {{"correct": true/false, "reason": "评估理由"}},
  "overall_assessment": "整体评估总结"
}}
"""

        # 调用Azure OpenAI
        response = self.client.chat.completions.create(
            model=self.model,
            messages=[
                {
                    "role": "system",
                    "content": "你是一个专业的客服对话审查评测专家，请严格按照要求的JSON格式输出评测结果。",
                },
                {"role": "user", "content": evaluation_prompt},
            ],
            temperature=0.1,
            max_tokens=2000,
            )

            # 解析响应
            evaluation_text = response.choices[0].message.content.strip()

            # 尝试解析JSON
            try:
                # 提取JSON部分（可能包含其他文本）
                start_idx = evaluation_text.find("{")
                end_idx = evaluation_text.rfind("}") + 1
                if start_idx != -1 and end_idx != -1:
                    json_text = evaluation_text[start_idx:end_idx]
                    evaluation_result = json.loads(json_text)
                else:
                    raise ValueError("No JSON found in response")
            except (json.JSONDecodeError, ValueError) as e:
                print(f"⚠️ JSON解析失败，使用原始文本: {e}")
                evaluation_result = {
                    "evaluation_text": evaluation_text,
                    "parse_error": str(e),
                }

        return {
            "status": "success",
            "evaluation": evaluation_result,
            "raw_response": evaluation_text,
        }

    def _evaluate_with_chunking(self, conversation_text: str, review_text: str) -> Dict[str, Any]:
        """使用分块处理长文本评测"""
        if not self.chunker:
            print("⚠️ Chonkie不可用，无法处理长文本")
            return {
                "status": "error",
                "error": "Chonkie not available for long text processing"
            }

        try:
            # 使用Chonkie分块
            chunks = self.chunker(conversation_text)
            print(f"📊 将对话分为 {len(chunks)} 个块进行评测")

            # 对每个块进行评测
            chunk_results = []
            for i, chunk in enumerate(chunks):
                print(f"🔄 评测第 {i+1}/{len(chunks)} 个块...")
                chunk_result = self._evaluate_single_text(chunk.text, review_text)
                chunk_results.append(chunk_result)

            # 合并结果
            merged_result = self._merge_chunk_evaluations(chunk_results)
            merged_result["chunk_count"] = len(chunks)
            merged_result["chunked"] = True

            return merged_result

        except Exception as e:
            print(f"❌ 分块评测失败: {e}")
            return {
                "status": "error",
                "error": f"Chunked evaluation failed: {str(e)}"
            }

    def _merge_chunk_evaluations(self, chunk_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """合并多个块的评测结果"""
        if not chunk_results:
            return {"status": "error", "error": "No chunk results to merge"}

        successful_results = [r for r in chunk_results if r.get("status") == "success"]

        if not successful_results:
            return {"status": "error", "error": "All chunk evaluations failed"}

        # 合并评测结果 - 采用多数投票或最严格的结果
        categories = ["key_contact", "sensitive_inquiry", "sensitive_reply", "government_inquiry", "internal_system"]
        merged_evaluation = {}

        for category in categories:
            category_results = []
            for result in successful_results:
                eval_data = result.get("evaluation", {})
                if category in eval_data:
                    category_results.append(eval_data[category])

            if category_results:
                # 采用最严格的评测结果（如果任何一个块认为不正确，则认为不正确）
                correct_votes = sum(1 for r in category_results if r.get("correct", False))
                total_votes = len(category_results)

                merged_evaluation[category] = {
                    "correct": correct_votes == total_votes,  # 所有块都认为正确才算正确
                    "reason": f"基于{total_votes}个块的评测，{correct_votes}个认为正确",
                    "chunk_details": category_results
                }

        # 生成整体评估
        overall_correct = sum(1 for cat in merged_evaluation.values() if cat.get("correct", False))
        total_categories = len(merged_evaluation)

        merged_evaluation["overall_assessment"] = (
            f"基于{len(successful_results)}个文本块的综合评测，"
            f"{overall_correct}/{total_categories}个维度评测正确"
        )

        return {
            "status": "success",
            "evaluation": merged_evaluation,
            "raw_response": f"Merged from {len(successful_results)} chunks"
        }


def evaluate_case_from_csv(
    case_row: Dict[str, str], text_format_dir: Path, evaluator: AzureEvaluator
) -> Dict[str, Any]:
    """基于CSV行数据和text_format文件进行案例评测"""
    case_id = case_row.get("case_id", "unknown")

    try:
        print(f"🔄 正在评测案例: {case_id}")

        # 从text_format目录加载对话内容
        conversation_content = load_text_format_case(case_id, text_format_dir)
        if not conversation_content:
            return {
                "case_id": case_id,
                "status": "error",
                "error": "Failed to load conversation content",
                "processed_at": datetime.now().isoformat(),
            }

        # 解析对话内容为消息列表
        messages = parse_text_format_content(conversation_content)
        print(f"   解析到 {len(messages)} 条消息")

        # 解析CSV中的审查结果
        review_result = parse_csv_row_to_review_result(case_row)

        # 构建模拟的result结构用于评测
        mock_result = {"id": case_id, "review_res": review_result}

        # 进行LLM评测
        evaluation = evaluator.evaluate_result(messages, mock_result)

        if evaluation["status"] == "success":
            print(f"✅ 案例 {case_id} 评测完成")
        else:
            print(
                f"⚠️ 案例 {case_id} 评测失败: {evaluation.get('error', 'Unknown error')}"
            )

        return {
            "case_id": case_id,
            "status": "success",
            "review_result": review_result,
            "evaluation": evaluation,
            "message_count": len(messages),
            "processed_at": datetime.now().isoformat(),
        }

    except Exception as e:
        print(f"❌ 案例 {case_id} 评测失败: {e}")
        return {
            "case_id": case_id,
            "status": "error",
            "error": str(e),
            "processed_at": datetime.now().isoformat(),
        }


def flatten_evaluation_result(evaluation: Dict[str, Any]) -> Dict[str, Any]:
    """将评测结果展平为CSV友好的格式"""
    flattened = {}

    if evaluation.get("status") == "success" and "evaluation" in evaluation:
        eval_data = evaluation["evaluation"]

        # 处理各个维度的评测结果
        for category in [
            "key_contact",
            "sensitive_inquiry",
            "sensitive_reply",
            "government_inquiry",
            "internal_system",
        ]:
            if category in eval_data and isinstance(eval_data[category], dict):
                flattened[f"eval_{category}_correct"] = eval_data[category].get(
                    "correct", None
                )
                flattened[f"eval_{category}_reason"] = eval_data[category].get(
                    "reason", ""
                )

        # 添加整体评估
        flattened["eval_overall_assessment"] = eval_data.get("overall_assessment", "")
        flattened["eval_status"] = "success"
    else:
        flattened["eval_status"] = evaluation.get("status", "unknown")
        flattened["eval_error"] = evaluation.get("error", "")

    return flattened


def save_evaluation_results_to_csv(results: List[Dict[str, Any]], output_file: str):
    """将评测结果保存为CSV格式"""
    if not results:
        print("⚠️ 没有结果可保存")
        return

    # 收集所有可能的字段名
    all_fields = set()
    csv_rows = []

    for result in results:
        row = {
            "case_id": result["case_id"],
            "status": result["status"],
            "message_count": result.get("message_count", 0),
            "processed_at": result["processed_at"],
        }

        if result["status"] == "success":
            # 添加评测结果
            if "evaluation" in result:
                flattened_eval = flatten_evaluation_result(result["evaluation"])
                row.update(flattened_eval)
        elif result["status"] == "error":
            row["error"] = result.get("error", "")

        csv_rows.append(row)
        all_fields.update(row.keys())

    # 排序字段名以确保一致性
    fieldnames = sorted(all_fields)

    # 写入CSV文件
    with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(csv_rows)

    print(f"📊 评测结果已保存到: {output_file}")
    print(f"   总记录数: {len(csv_rows)}")
    print(f"   字段数: {len(fieldnames)}")


def calculate_evaluation_statistics(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """计算评测统计信息"""
    stats = {
        "total_cases": len(results),
        "successful_evaluations": 0,
        "failed_evaluations": 0,
        "category_accuracy": {},
        "overall_accuracy": 0.0,
    }

    categories = [
        "key_contact",
        "sensitive_inquiry",
        "sensitive_reply",
        "government_inquiry",
        "internal_system",
    ]
    category_stats = {cat: {"correct": 0, "total": 0} for cat in categories}

    for result in results:
        if result["status"] == "success" and "evaluation" in result:
            evaluation = result["evaluation"]

            if evaluation.get("status") == "success" and "evaluation" in evaluation:
                stats["successful_evaluations"] += 1
                eval_data = evaluation["evaluation"]

                # 统计各个维度的准确性
                for category in categories:
                    if category in eval_data and isinstance(eval_data[category], dict):
                        category_stats[category]["total"] += 1
                        if eval_data[category].get("correct", False):
                            category_stats[category]["correct"] += 1
            else:
                stats["failed_evaluations"] += 1
        else:
            stats["failed_evaluations"] += 1

    # 计算准确率
    total_correct = 0
    total_evaluated = 0

    for category, data in category_stats.items():
        if data["total"] > 0:
            accuracy = data["correct"] / data["total"]
            stats["category_accuracy"][category] = {
                "correct": data["correct"],
                "total": data["total"],
                "accuracy": accuracy,
            }
            total_correct += data["correct"]
            total_evaluated += data["total"]
        else:
            stats["category_accuracy"][category] = {
                "correct": 0,
                "total": 0,
                "accuracy": 0.0,
            }

    # 计算整体准确率
    if total_evaluated > 0:
        stats["overall_accuracy"] = total_correct / total_evaluated

    return stats


def main():
    """主函数 - 基于CSV结果文件进行完整批量评测"""
    print("🚀 基于CSV结果文件进行Azure OpenAI批量评测")
    print("=" * 60)

    # 设置路径
    project_root = Path(__file__).parent.parent
    text_format_dir = project_root / "data" / "text_format"
    # csv_file = project_root / "results" / "individual_cases_results_20250726_112511.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250726_153000.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250728_155208.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250728_195352.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250729_105147.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250729_154732.csv"
    # csv_file = project_root / "results" / "individual_cases_results_20250730_095903.csv"
    csv_file = project_root / "results" / "individual_cases_results_20250731_172355.csv"
    results_dir = project_root / "results"

    # 确保结果目录存在
    results_dir.mkdir(exist_ok=True)

    # 生成输出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    json_output_file = results_dir / f"evaluation_results_{timestamp}.json"
    csv_output_file = results_dir / f"evaluation_results_{timestamp}.csv"
    stats_output_file = results_dir / f"evaluation_statistics_{timestamp}.json"

    # 检查输入文件和目录
    if not csv_file.exists():
        print(f"❌ CSV结果文件不存在: {csv_file}")
        return

    if not text_format_dir.exists():
        print(f"❌ text_format目录不存在: {text_format_dir}")
        return

    print(f"📁 CSV文件: {csv_file}")
    print(f"📁 对话内容目录: {text_format_dir}")

    # 加载CSV结果
    print("📊 加载CSV结果文件...")
    csv_results = load_csv_results(str(csv_file))
    if not csv_results:
        print("❌ 无法加载CSV结果文件")
        return

    print(f"✅ 加载了 {len(csv_results)} 条结果记录")

    # 初始化评测器
    print("🤖 初始化Azure OpenAI评测器...")
    try:
        evaluator = AzureEvaluator()
        print("✅ Azure OpenAI评测器初始化成功")
    except Exception as e:
        print(f"❌ Azure OpenAI评测器初始化失败: {e}")
        return

    # 处理所有案例
    results = []
    start_time = time.time()

    try:
        for i, case_row in enumerate(csv_results, 1):
            case_id = case_row.get("case_id", "unknown")
            print(f"\n📋 进度: {i}/{len(csv_results)} - 案例ID: {case_id}")

            # 评测单个案例
            result = evaluate_case_from_csv(case_row, text_format_dir, evaluator)
            results.append(result)

            # 添加延迟以避免API过载
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断处理")
    except Exception as e:
        print(f"\n❌ 处理过程中发生错误: {e}")

    # 计算统计信息
    end_time = time.time()
    total_time = end_time - start_time
    successful_cases = sum(1 for r in results if r["status"] == "success")
    failed_cases = len(results) - successful_cases

    print(f"\n📊 处理完成统计:")
    print(f"   总处理时间: {total_time:.2f} 秒")
    print(f"   成功案例: {successful_cases}")
    print(f"   失败案例: {failed_cases}")
    if results:
        print(f"   平均处理时间: {total_time / len(results):.2f} 秒/案例")

    # 计算评测统计
    eval_stats = calculate_evaluation_statistics(results)

    print(f"\n🤖 评测统计:")
    print(f"   成功评测: {eval_stats['successful_evaluations']}")
    print(f"   失败评测: {eval_stats['failed_evaluations']}")
    print(f"   整体准确率: {eval_stats['overall_accuracy']:.2%}")

    print(f"\n📈 各维度准确率:")
    for category, stats in eval_stats["category_accuracy"].items():
        print(
            f"   {category}: {stats['correct']}/{stats['total']} ({stats['accuracy']:.2%})"
        )

    # 保存结果
    print(f"\n💾 保存结果...")

    # 保存JSON格式
    with open(json_output_file, "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"📄 JSON结果已保存到: {json_output_file}")

    # 保存CSV格式
    save_evaluation_results_to_csv(results, str(csv_output_file))

    # 保存统计信息
    with open(stats_output_file, "w", encoding="utf-8") as f:
        json.dump(eval_stats, f, ensure_ascii=False, indent=2)
    print(f"📊 评测统计已保存到: {stats_output_file}")

    print(f"\n🎉 批量评测完成!")


if __name__ == "__main__":
    main()

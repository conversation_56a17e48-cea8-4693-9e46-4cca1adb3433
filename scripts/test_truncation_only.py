#!/usr/bin/env python3
"""
只测试截断功能，不涉及完整pipeline
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dc_ai_red_line_review.utils import get_token_count


def truncate_message_content(msg_content: str, max_tokens: int) -> tuple[str, bool]:
    """简化版的消息截断功能"""
    # 首先检查是否需要截断
    current_tokens = get_token_count(msg_content)
    if current_tokens <= max_tokens:
        return msg_content, False

    print(f"消息超过 {max_tokens} tokens ({current_tokens})，开始截断...")

    # 使用保守的字符/token比例进行初步截断
    target_chars = max_tokens * 3

    # 进行智能截断，尝试在句子边界处截断
    truncated_content = msg_content[:target_chars]

    # 寻找最近的句子结束符
    sentence_endings = ["。", "！", "？", ".", "!", "?", "\n"]
    best_cut_pos = target_chars

    # 在最后200个字符内寻找合适的截断点
    search_start = max(0, target_chars - 200)
    for ending in sentence_endings:
        pos = truncated_content.rfind(ending, search_start)
        if pos > search_start:
            best_cut_pos = pos + 1
            break

    # 应用截断
    final_content = msg_content[:best_cut_pos].strip()

    # 验证截断后的token数，如果还是超限则进一步截断
    final_tokens = get_token_count(final_content)
    iteration = 0
    while final_tokens > max_tokens and iteration < 5:
        # 进一步减少字符数，每次减少20%
        reduction_factor = 0.8
        new_target = int(len(final_content) * reduction_factor)
        final_content = final_content[:new_target].strip()
        final_tokens = get_token_count(final_content)
        iteration += 1
        print(f"  迭代 {iteration}: 减少到 {final_tokens} tokens")

    print(f"截断完成: {current_tokens} -> {final_tokens} tokens")
    return final_content, True


def test_truncation():
    """测试截断功能"""
    print("🧪 截断功能测试")
    print("=" * 50)

    # 测试1: 正常长度消息
    normal_msg = "这是一条正常长度的消息"
    print(f"\n测试1 - 正常消息:")
    print(f"原始: {len(normal_msg)} 字符, {get_token_count(normal_msg)} tokens")

    truncated, was_truncated = truncate_message_content(normal_msg, 7000)
    print(f"结果: {'截断' if was_truncated else '未截断'}")

    # 测试2: 超长消息
    long_msg = "这是一条非常长的消息，用来测试截断功能。" * 1000
    print(f"\n测试2 - 超长消息:")
    print(f"原始: {len(long_msg):,} 字符, {get_token_count(long_msg):,} tokens")

    truncated, was_truncated = truncate_message_content(long_msg, 7000)
    print(f"结果: {'截断' if was_truncated else '未截断'}")
    print(f"截断后: {len(truncated):,} 字符, {get_token_count(truncated):,} tokens")

    # 验证截断结果
    final_tokens = get_token_count(truncated)
    if final_tokens <= 7000:
        print("✅ 截断成功，在限制内")
    else:
        print(f"❌ 截断失败，仍超限: {final_tokens}")

    # 计算截断比例
    if was_truncated:
        reduction_pct = (1 - len(truncated) / len(long_msg)) * 100
        print(f"截断比例: {reduction_pct:.1f}%")


def test_edge_cases():
    """测试边界情况"""
    print(f"\n🔍 边界情况测试")
    print("=" * 50)

    # 测试空消息
    empty_msg = ""
    truncated, was_truncated = truncate_message_content(empty_msg, 7000)
    print(f"空消息: {'截断' if was_truncated else '未截断'}")

    # 测试刚好在限制边缘的消息
    # 创建一个约7000 token的消息
    edge_msg = "这是一条边缘长度的消息。" * 700  # 约7000 tokens
    edge_tokens = get_token_count(edge_msg)
    print(f"边缘消息: {edge_tokens} tokens")

    truncated, was_truncated = truncate_message_content(edge_msg, 7000)
    print(f"结果: {'截断' if was_truncated else '未截断'}")


if __name__ == "__main__":
    test_truncation()
    test_edge_cases()
    print("\n✅ 截断功能测试完成")

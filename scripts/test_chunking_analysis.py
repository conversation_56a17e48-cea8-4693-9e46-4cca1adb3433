#!/usr/bin/env python3
"""
测试和分析文本切分的合理性
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dc_ai_red_line_review.utils import ChonkieTextChunker, get_token_count


def create_test_messages():
    """创建测试消息数据"""
    return [
        {"id": 1, "type": "USER", "msg": "你好，我想咨询一下关于账户安全的问题"},
        {"id": 2, "type": "AGENT", "msg": "您好！我是客服小王，很高兴为您服务。请问您遇到了什么账户安全问题呢？"},
        {"id": 3, "type": "USER", "msg": "我发现我的账户有一些异常登录记录，担心账户被盗用了"},
        {"id": 4, "type": "AGENT", "msg": "我理解您的担心。为了保护您的账户安全，我需要先验证您的身份。请提供您的注册手机号码后四位。"},
        {"id": 5, "type": "USER", "msg": "手机号后四位是1234"},
        {"id": 6, "type": "AGENT", "msg": "谢谢您的配合。我已经为您查询了账户登录记录，确实发现了一些异常IP地址的登录。建议您立即修改密码并开启二次验证。"},
        {"id": 7, "type": "USER", "msg": "好的，我应该怎么操作呢？"},
        {"id": 8, "type": "AGENT", "msg": "请按照以下步骤操作：1. 登录您的账户；2. 进入安全设置页面；3. 点击修改密码；4. 设置一个包含大小写字母、数字和特殊字符的强密码；5. 开启短信验证或谷歌验证器。"},
        {"id": 9, "type": "USER", "msg": "我按照您说的操作了，密码已经修改成功，也开启了短信验证"},
        {"id": 10, "type": "AGENT", "msg": "很好！您的账户安全性已经大大提升。另外，我建议您定期检查登录记录，如果发现异常请及时联系我们。还有其他问题需要帮助吗？"},
        {"id": 11, "type": "USER", "msg": "没有了，谢谢您的帮助"},
        {"id": 12, "type": "AGENT", "msg": "不客气！如果您还有其他问题，随时可以联系我们。祝您使用愉快！"}
    ]


def create_long_test_messages():
    """创建长消息测试数据"""
    long_messages = []
    
    # 创建一个很长的用户消息
    long_user_msg = "我遇到了一个非常复杂的问题，" * 200  # 重复200次
    long_messages.append({"id": 1, "type": "USER", "msg": long_user_msg})
    
    # 创建一个很长的客服回复
    long_agent_msg = "感谢您的咨询，我来为您详细解答这个问题。" * 150  # 重复150次
    long_messages.append({"id": 2, "type": "AGENT", "msg": long_agent_msg})
    
    # 添加更多正常长度的消息
    for i in range(3, 50):
        msg_type = "USER" if i % 2 == 1 else "AGENT"
        msg_content = f"这是第{i}条消息，内容相对较短但足够测试分块效果。" * 5
        long_messages.append({"id": i, "type": msg_type, "msg": msg_content})
    
    return long_messages


def analyze_chunking_quality(chunks, chunk_tokens, messages=None):
    """分析切分质量"""
    print("\n📊 切分质量分析")
    print("=" * 60)
    
    # 基本统计
    total_chunks = len(chunks)
    total_tokens = sum(chunk_tokens)
    avg_tokens = total_tokens / total_chunks if total_chunks > 0 else 0
    
    print(f"总分块数: {total_chunks}")
    print(f"总token数: {total_tokens:,}")
    print(f"平均每块token数: {avg_tokens:.1f}")
    
    # Token分布分析
    max_tokens = max(chunk_tokens) if chunk_tokens else 0
    min_tokens = min(chunk_tokens) if chunk_tokens else 0
    
    print(f"最大分块token数: {max_tokens:,}")
    print(f"最小分块token数: {min_tokens:,}")
    print(f"Token利用率: {avg_tokens/8000*100:.1f}%" if avg_tokens > 0 else "0%")
    
    # 检查是否有超限的分块
    over_limit = [i for i, tokens in enumerate(chunk_tokens) if tokens > 8000]
    if over_limit:
        print(f"⚠️ 超过8000 token限制的分块: {over_limit}")
    else:
        print("✅ 所有分块都在token限制内")
    
    # 如果是消息切分，分析消息分布
    if messages and isinstance(chunks[0], list):
        print(f"\n📨 消息分布分析")
        for i, chunk in enumerate(chunks):
            if isinstance(chunk, list):
                first_id = chunk[0]["id"] if chunk else "N/A"
                last_id = chunk[-1]["id"] if chunk else "N/A"
                print(f"  分块 {i+1}: {len(chunk)} 条消息 (ID {first_id}-{last_id}), {chunk_tokens[i]:,} tokens")


def test_text_chunking():
    """测试文本切分"""
    print("🔪 测试文本切分")
    print("=" * 60)
    
    # 创建长文本
    test_text = "这是一个测试文本。" * 1000  # 创建一个较长的文本
    
    chunker = ChonkieTextChunker(chunk_size=8000, chunk_overlap=1000)
    
    print(f"原始文本长度: {len(test_text):,} 字符")
    print(f"估算token数: {get_token_count(test_text):,}")
    
    chunks, chunk_tokens = chunker.chunk_text(test_text)
    
    analyze_chunking_quality(chunks, chunk_tokens)
    
    return chunks, chunk_tokens


def test_message_chunking():
    """测试消息切分"""
    print("\n🔪 测试消息切分")
    print("=" * 60)
    
    # 测试正常长度消息
    print("\n--- 正常长度消息测试 ---")
    normal_messages = create_test_messages()
    
    chunker = ChonkieTextChunker(chunk_size=8000, chunk_overlap=1000)
    
    print(f"消息数量: {len(normal_messages)}")
    
    # 计算总token数
    total_tokens = 0
    for msg in normal_messages:
        formatted_msg = f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
        total_tokens += get_token_count(formatted_msg)
    
    print(f"总token数: {total_tokens:,}")
    
    chunks, chunk_tokens = chunker.chunk_messages(normal_messages, 8000)
    
    analyze_chunking_quality(chunks, chunk_tokens, normal_messages)
    
    # 测试长消息
    print("\n--- 长消息测试 ---")
    long_messages = create_long_test_messages()
    
    print(f"消息数量: {len(long_messages)}")
    
    # 计算总token数
    total_tokens = 0
    for msg in long_messages:
        formatted_msg = f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
        total_tokens += get_token_count(formatted_msg)
    
    print(f"总token数: {total_tokens:,}")
    
    chunks, chunk_tokens = chunker.chunk_messages(long_messages, 8000)
    
    analyze_chunking_quality(chunks, chunk_tokens, long_messages)
    
    return chunks, chunk_tokens


def test_edge_cases():
    """测试边界情况"""
    print("\n🔪 测试边界情况")
    print("=" * 60)
    
    chunker = ChonkieTextChunker(chunk_size=8000, chunk_overlap=1000)
    
    # 测试空消息
    print("--- 空消息测试 ---")
    empty_chunks, empty_tokens = chunker.chunk_messages([], 8000)
    print(f"空消息结果: {len(empty_chunks)} 分块, {sum(empty_tokens)} tokens")
    
    # 测试单条超长消息
    print("\n--- 单条超长消息测试 ---")
    super_long_msg = "这是一条超级长的消息。" * 2000  # 创建一条很长的消息
    super_long_messages = [{"id": 1, "type": "USER", "msg": super_long_msg}]
    
    formatted_msg = f"<|im_start|>USER\n{super_long_msg}<|im_end|>"
    msg_tokens = get_token_count(formatted_msg)
    print(f"单条消息token数: {msg_tokens:,}")
    
    if msg_tokens > 15000:
        print("⚠️ 单条消息超过15000 token限制")
    else:
        chunks, chunk_tokens = chunker.chunk_messages(super_long_messages, 8000)
        analyze_chunking_quality(chunks, chunk_tokens, super_long_messages)


def main():
    """主函数"""
    print("🧪 文本切分合理性分析")
    print("=" * 80)
    
    # 测试文本切分
    test_text_chunks, test_text_tokens = test_text_chunking()
    
    # 测试消息切分
    test_msg_chunks, test_msg_tokens = test_message_chunking()
    
    # 测试边界情况
    test_edge_cases()
    
    print("\n📋 总结和建议")
    print("=" * 60)
    print("1. 当前切分策略能够有效处理大部分场景")
    print("2. 消息切分保持了对话的完整性")
    print("3. Token估算相对准确，有适当的安全边距")
    print("4. 建议监控实际使用中的token使用情况")
    print("5. 对于超长单条消息需要特殊处理")


if __name__ == "__main__":
    main()

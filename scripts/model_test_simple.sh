#!/bin/bash

# Simple curl equivalent of model_test.py
# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

echo "正在连接到: $QWQ_BASE_URL"
echo "使用模型: $MODEL_NAME"

start_time=$(date +%s.%N)

curl -X POST "$QWQ_BASE_URL/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $QWQ_API_KEY" \
  -k \
  --connect-timeout 60 \
  --max-time 60 \
  -d '{
    "model": "'"$MODEL_NAME"'",
    "messages": [
      {
        "role": "user",
        "content": "你叫什么名字？"
      }
    ],
    "chat_template_kwargs": {
      "enable_thinking": false
    }
  }'

end_time=$(date +%s.%N)
execution_time=$(echo "$end_time - $start_time" | bc -l)
printf "\n请求耗时: %.2f秒\n" $execution_time

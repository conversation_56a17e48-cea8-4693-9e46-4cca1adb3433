#!/usr/bin/env python3
"""
时间分布分析脚本 - 输出到文件
"""

import json
import sys
import time
import statistics
from pathlib import Path
from datetime import datetime

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dc_ai_red_line_review import BasicPipeline


def analyze_and_process_dataset(data, dataset_name, output_file):
    """分析并处理数据集"""
    
    output_file.write(f"\n{'='*60}\n")
    output_file.write(f"📊 {dataset_name} 时间分布分析\n")
    output_file.write(f"{'='*60}\n")
    
    pipeline = BasicPipeline()
    case_data = []
    
    for i, (case_id, messages) in enumerate(data.items(), 1):
        start_time = time.time()
        
        try:
            print(f"🔄 [{i:3d}/{len(data)}] 处理案例 {case_id} ({len(messages)} 消息)", end=" ")
            
            # 处理案例
            result = pipeline.run(messages=messages, caseId=case_id)
            
            duration = time.time() - start_time
            case_data.append({
                'case_id': case_id,
                'message_count': len(messages),
                'duration': duration,
                'success': True
            })
            
            print(f"✅ {duration:.2f}s")
            
        except Exception as e:
            duration = time.time() - start_time
            case_data.append({
                'case_id': case_id,
                'message_count': len(messages),
                'duration': duration,
                'success': False,
                'error': str(e)
            })
            print(f"❌ {duration:.2f}s")
    
    # 分析结果
    successful_cases = [c for c in case_data if c['success']]
    durations = [c['duration'] for c in successful_cases]
    
    if not durations:
        output_file.write("❌ 没有成功的案例可供分析\n")
        return
    
    # 基本统计
    output_file.write(f"\n📈 基本统计信息:\n")
    output_file.write(f"   总案例数: {len(case_data)}\n")
    output_file.write(f"   成功案例: {len(successful_cases)}\n")
    output_file.write(f"   平均时间: {statistics.mean(durations):.2f} 秒\n")
    output_file.write(f"   中位数: {statistics.median(durations):.2f} 秒\n")
    if len(durations) > 1:
        output_file.write(f"   标准差: {statistics.stdev(durations):.2f} 秒\n")
    output_file.write(f"   最小值: {min(durations):.2f} 秒\n")
    output_file.write(f"   最大值: {max(durations):.2f} 秒\n")
    
    # 时间区间分布
    output_file.write(f"\n⏱️  时间区间分布:\n")
    time_ranges = [
        (0, 2, "极快"),
        (2, 5, "快速"), 
        (5, 10, "正常"),
        (10, 20, "较慢"),
        (20, 50, "慢"),
        (50, float('inf'), "很慢")
    ]
    
    for min_time, max_time, label in time_ranges:
        if max_time == float('inf'):
            count = len([d for d in durations if d >= min_time])
            if count > 0:
                percentage = count / len(durations) * 100
                output_file.write(f"   {label} (≥{min_time}s): {count} 案例 ({percentage:.1f}%)\n")
        else:
            count = len([d for d in durations if min_time <= d < max_time])
            if count > 0:
                percentage = count / len(durations) * 100
                output_file.write(f"   {label} ({min_time}-{max_time}s): {count} 案例 ({percentage:.1f}%)\n")
    
    # 消息数量与处理时间的关系
    output_file.write(f"\n💬 消息数量与处理时间关系:\n")
    
    message_groups = {}
    for case in successful_cases:
        msg_count = case['message_count']
        duration = case['duration']
        
        if msg_count <= 10:
            group = "1-10条"
        elif msg_count <= 20:
            group = "11-20条"
        elif msg_count <= 50:
            group = "21-50条"
        else:
            group = "50+条"
        
        if group not in message_groups:
            message_groups[group] = []
        message_groups[group].append(duration)
    
    for group, times in message_groups.items():
        avg_time = statistics.mean(times)
        output_file.write(f"   {group} 消息: {len(times)} 案例, 平均 {avg_time:.2f}s\n")
    
    # 详细案例列表
    output_file.write(f"\n📋 详细案例列表 (按处理时间排序):\n")
    successful_cases.sort(key=lambda x: x['duration'])
    
    output_file.write(f"{'序号':<4} {'案例ID':<12} {'消息数':<6} {'耗时(秒)':<8} {'每消息耗时':<10}\n")
    output_file.write("-" * 50 + "\n")
    
    for i, case in enumerate(successful_cases, 1):
        time_per_msg = case['duration'] / case['message_count'] if case['message_count'] > 0 else 0
        output_file.write(f"{i:<4} {case['case_id']:<12} {case['message_count']:<6} {case['duration']:<8.2f} {time_per_msg:<10.3f}\n")
    
    # 异常案例分析
    slow_cases = [c for c in successful_cases if c['duration'] > 20]
    if slow_cases:
        output_file.write(f"\n🐌 慢速案例分析 (>20秒):\n")
        for case in slow_cases:
            time_per_msg = case['duration'] / case['message_count']
            output_file.write(f"   案例 {case['case_id']}: {case['duration']:.2f}s ({case['message_count']} 消息, {time_per_msg:.3f}s/消息)\n")
    
    fast_cases = [c for c in successful_cases if c['duration'] < 3]
    if fast_cases:
        output_file.write(f"\n🏃 快速案例分析 (<3秒):\n")
        for case in fast_cases:
            time_per_msg = case['duration'] / case['message_count']
            output_file.write(f"   案例 {case['case_id']}: {case['duration']:.2f}s ({case['message_count']} 消息, {time_per_msg:.3f}s/消息)\n")


def main():
    """主函数"""
    print("🚀 开始时间分布分析")
    
    # 创建输出文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file_path = project_root / "results" / f"time_distribution_analysis_{timestamp}.txt"
    output_file_path.parent.mkdir(exist_ok=True)
    
    with open(output_file_path, 'w', encoding='utf-8') as output_file:
        output_file.write(f"时间分布分析报告\n")
        output_file.write(f"生成时间: {datetime.now().isoformat()}\n")
        output_file.write(f"="*60 + "\n")
        
        # 处理第一个数据集
        data_file_v1 = project_root / "data" / "converted_data.json"
        try:
            with open(data_file_v1, 'r', encoding='utf-8') as f:
                data_v1 = json.load(f)
            print(f"📂 处理 converted_data.json ({len(data_v1)} 案例)")
            analyze_and_process_dataset(data_v1, "converted_data.json", output_file)
        except Exception as e:
            print(f"❌ 处理 converted_data.json 失败: {e}")
            output_file.write(f"❌ 处理 converted_data.json 失败: {e}\n")
        
        # 处理第二个数据集
        data_file_v2 = project_root / "data" / "converted_data_v2.json"
        try:
            with open(data_file_v2, 'r', encoding='utf-8') as f:
                data_v2 = json.load(f)
            print(f"📂 处理 converted_data_v2.json ({len(data_v2)} 案例)")
            analyze_and_process_dataset(data_v2, "converted_data_v2.json", output_file)
        except Exception as e:
            print(f"❌ 处理 converted_data_v2.json 失败: {e}")
            output_file.write(f"❌ 处理 converted_data_v2.json 失败: {e}\n")
    
    print(f"🎉 分析完成! 结果保存到: {output_file_path}")


if __name__ == "__main__":
    main()

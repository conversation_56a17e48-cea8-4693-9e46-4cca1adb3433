#!/usr/bin/env python3
"""
简单的截断功能测试
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dc_ai_red_line_review.main_pipe import BasicPipeline
from dc_ai_red_line_review.utils import get_token_count


def test_truncation():
    """测试截断功能"""
    print("🧪 简单截断功能测试")
    print("=" * 50)
    
    # 创建一个超长消息
    long_msg = "这是一条非常长的消息，用来测试截断功能。" * 1000  # 约20000+ tokens
    
    print(f"原始消息长度: {len(long_msg):,} 字符")
    
    # 计算原始token数
    original_tokens = get_token_count(long_msg)
    print(f"原始token数: {original_tokens:,}")
    
    # 创建pipeline并测试截断
    pipeline = BasicPipeline()
    
    # 测试截断功能
    truncated_content, was_truncated = pipeline._truncate_message_content(long_msg, 7000)
    
    print(f"\n截断结果:")
    print(f"是否被截断: {was_truncated}")
    print(f"截断后长度: {len(truncated_content):,} 字符")
    
    # 计算截断后的token数
    truncated_tokens = get_token_count(truncated_content)
    print(f"截断后token数: {truncated_tokens:,}")
    
    # 验证是否在限制内
    if truncated_tokens <= 7000:
        print("✅ 截断成功，在7000 token限制内")
    else:
        print(f"❌ 截断失败，仍然超过限制: {truncated_tokens}")
    
    # 显示截断比例
    if was_truncated:
        reduction_pct = (1 - len(truncated_content) / len(long_msg)) * 100
        print(f"截断比例: {reduction_pct:.1f}%")


def test_with_real_messages():
    """使用真实消息测试完整流程"""
    print("\n🔄 真实消息测试")
    print("=" * 50)
    
    # 创建包含超长消息的测试数据
    messages = [
        {"id": 1, "type": "USER", "msg": "你好"},
        {"id": 2, "type": "USER", "msg": "这是一条超长消息。" * 2000},  # 超长消息
        {"id": 3, "type": "AGENT", "msg": "我来帮您解决问题"}
    ]
    
    print(f"消息数量: {len(messages)}")
    
    # 分析原始消息
    for i, msg in enumerate(messages):
        formatted_msg = f"<|im_start|>{msg['type']}\n{msg['msg']}<|im_end|>"
        tokens = get_token_count(formatted_msg)
        status = " ⚠️ OVERSIZED" if tokens > 7000 else ""
        print(f"消息 {i+1}: {len(msg['msg']):,} 字符, {tokens:,} tokens{status}")
    
    # 运行pipeline
    try:
        pipeline = BasicPipeline()
        result = pipeline.run(messages=messages, caseId="simple_test")
        print("\n✅ Pipeline处理成功")
        print(f"返回结果类型: {type(result)}")
        print(f"包含的键: {list(result.keys()) if isinstance(result, dict) else 'N/A'}")
    except Exception as e:
        print(f"\n❌ Pipeline处理失败: {e}")


if __name__ == "__main__":
    test_truncation()
    test_with_real_messages()

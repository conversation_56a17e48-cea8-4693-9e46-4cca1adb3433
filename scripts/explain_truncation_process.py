#!/usr/bin/env python3
"""
详细展示截断过程的每一步
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dc_ai_red_line_review.utils import get_token_count


def detailed_truncation_demo():
    """详细演示截断过程"""
    print("🔍 截断过程详细分析")
    print("=" * 80)
    
    # 创建一个超长消息
    test_msg = "这是一条非常长的消息，需要截断处理。" * 1000  # 约8000+ tokens
    
    print(f"📋 配置参数:")
    print(f"- MAX_SINGLE_MESSAGE_TOKENS = 7,000")
    print(f"- CHUNK_SIZE_TOKENS = 8,000") 
    print(f"- 安全边界 = 1,000 tokens")
    
    print(f"\n📊 原始消息分析:")
    original_chars = len(test_msg)
    original_tokens = get_token_count(test_msg)
    print(f"- 字符数: {original_chars:,}")
    print(f"- Token数: {original_tokens:,}")
    print(f"- 字符/Token比例: {original_chars/original_tokens:.2f}")
    
    # 检查是否需要截断
    max_tokens = 7000
    if original_tokens <= max_tokens:
        print(f"✅ 消息在限制内，无需截断")
        return
    
    print(f"\n⚠️ 消息超过 {max_tokens} tokens，需要截断")
    print(f"超出: {original_tokens - max_tokens:,} tokens ({((original_tokens - max_tokens) / max_tokens * 100):.1f}%)")
    
    print(f"\n🔪 截断过程:")
    
    # 步骤1: 初步截断
    print(f"\n步骤1: 保守估算截断")
    target_chars = max_tokens * 3  # 保守估计：3字符/token
    print(f"- 目标字符数: {max_tokens} tokens × 3 = {target_chars:,} 字符")
    
    if original_chars <= target_chars:
        print(f"- 原始字符数({original_chars:,}) <= 目标字符数({target_chars:,})")
        print(f"- 但token数({original_tokens:,}) > 限制({max_tokens})")
        print(f"- 说明这段文本的token密度较高")
    
    # 进行初步截断
    truncated_content = test_msg[:target_chars]
    step1_chars = len(truncated_content)
    step1_tokens = get_token_count(truncated_content)
    
    print(f"- 初步截断结果: {step1_chars:,} 字符, {step1_tokens:,} tokens")
    
    # 步骤2: 句子边界优化
    print(f"\n步骤2: 句子边界优化")
    sentence_endings = ["。", "！", "？", ".", "!", "?", "\n"]
    best_cut_pos = target_chars
    search_start = max(0, target_chars - 200)
    
    found_boundary = False
    for ending in sentence_endings:
        pos = truncated_content.rfind(ending, search_start)
        if pos > search_start:
            best_cut_pos = pos + 1
            found_boundary = True
            print(f"- 找到句子边界 '{ending}' 在位置 {pos}")
            break
    
    if not found_boundary:
        print(f"- 未找到合适的句子边界，使用字符截断")
    
    # 应用句子边界截断
    final_content = test_msg[:best_cut_pos].strip()
    step2_chars = len(final_content)
    step2_tokens = get_token_count(final_content)
    
    print(f"- 边界优化结果: {step2_chars:,} 字符, {step2_tokens:,} tokens")
    
    # 步骤3: 迭代优化
    print(f"\n步骤3: 迭代优化 (确保在限制内)")
    
    iteration = 0
    current_content = final_content
    current_tokens = step2_tokens
    
    while current_tokens > max_tokens and iteration < 5:
        iteration += 1
        reduction_factor = 0.8  # 每次减少20%
        new_target = int(len(current_content) * reduction_factor)
        current_content = current_content[:new_target].strip()
        current_tokens = get_token_count(current_content)
        
        print(f"- 迭代 {iteration}: 减少20% → {len(current_content):,} 字符, {current_tokens:,} tokens")
        
        if current_tokens <= max_tokens:
            print(f"  ✅ 达到限制内")
            break
    
    # 最终结果
    print(f"\n📈 截断总结:")
    print(f"- 原始: {original_chars:,} 字符, {original_tokens:,} tokens")
    print(f"- 最终: {len(current_content):,} 字符, {current_tokens:,} tokens")
    print(f"- 字符减少: {((original_chars - len(current_content)) / original_chars * 100):.1f}%")
    print(f"- Token减少: {((original_tokens - current_tokens) / original_tokens * 100):.1f}%")
    print(f"- 最终token密度: {len(current_content) / current_tokens:.2f} 字符/token")
    
    # 验证结果
    print(f"\n✅ 验证结果:")
    if current_tokens <= max_tokens:
        print(f"- 截断成功: {current_tokens:,} ≤ {max_tokens:,} tokens")
        margin = max_tokens - current_tokens
        print(f"- 安全边界: {margin:,} tokens ({(margin / max_tokens * 100):.1f}%)")
    else:
        print(f"- ❌ 截断失败: {current_tokens:,} > {max_tokens:,} tokens")


def explain_why_truncation_needed():
    """解释为什么需要截断"""
    print(f"\n💡 为什么8,510 tokens需要截断？")
    print("=" * 60)
    
    print(f"1. **限制设置**:")
    print(f"   - 单条消息限制: 7,000 tokens")
    print(f"   - 分块大小限制: 8,000 tokens")
    print(f"   - 安全边界: 1,000 tokens")
    
    print(f"\n2. **为什么是7,000而不是8,000？**")
    print(f"   - 消息格式化开销: ~4 tokens (<|im_start|>USER\\n....<|im_end|>)")
    print(f"   - 分块重叠处理: 需要预留空间")
    print(f"   - 安全缓冲: 避免边界情况")
    
    print(f"\n3. **截断到6,807的原因**:")
    print(f"   - 算法优先保证在限制内")
    print(f"   - 迭代截断每次减少20%")
    print(f"   - 在句子边界截断可能损失一些token")
    print(f"   - 保守策略确保稳定性")
    
    print(f"\n4. **Token vs 字符**:")
    print(f"   - 中文: ~2.5字符/token")
    print(f"   - 英文: ~4字符/token")
    print(f"   - 混合文本: 比例会变化")
    print(f"   - 分块必须按token计算，因为模型限制是token-based")


if __name__ == "__main__":
    detailed_truncation_demo()
    explain_why_truncation_needed()
